<template>
  <view class="container pb-100rpx">
    <uni-nav-bar
      :fixed="true"
      statusBar="true"
      :border="false"
      title="意向职位管理"
      left-icon="back"
      :backgroundColor="'#ffffff'"
      @clickLeft="goBack"
    />

    <!-- 已选职位 -->
    <view class="selected-jobs px-30rpx py-30rpx mb-20rpx">
      <view class="section-title flex-x-between mb-20rpx">
        <text class="text-32rpx font-bold"
          >已选职位（{{ selectedJobs.length }}/5）</text
        >
        <text
          v-if="selectedJobs.length > 0"
          class="text-26rpx text-primary"
          @tap="clearAllSelected"
          >清空</text
        >
      </view>

      <view
        v-if="selectedJobs.length === 0"
        class="empty-tips flex-y-center py-40rpx"
      >
        <text class="i-carbon-information text-grey mb-10rpx text-48rpx"></text>
        <text class="text-28rpx text-secondary"
          >暂无已选职位，请在下方添加</text
        >
      </view>

      <view v-else class="selected-list flex flex-wrap">
        <view
          v-for="(job, index) in selectedJobs"
          :key="index"
          class="selected-item flex-x-between"
        >
          <text class="job-name">{{ job.name }}</text>
          <text
            class="i-carbon-close-filled remove-icon"
            @tap="removeJob(index)"
          ></text>
        </view>
      </view>
    </view>

    <!-- 职位分类 -->
    <view class="job-categories px-30rpx">
      <view class="section-title mb-20rpx">
        <text class="text-32rpx font-bold">选择职位分类</text>
      </view>

      <view class="category-list">
        <view
          v-for="(category, index) in jobCategories"
          :key="index"
          class="category-item"
        >
          <view class="category-title py-20rpx">
            <text class="text-30rpx font-bold">{{ category.name }}</text>
          </view>

          <view class="job-list flex flex-wrap">
            <view
              v-for="(job, jobIndex) in category.jobs"
              :key="jobIndex"
              class="job-item"
              :class="{ 'job-item-selected': isJobSelected(job) }"
              @tap="toggleJobSelection(job)"
            >
              {{ job.name }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部保存按钮 -->
    <view class="save-btn-wrapper px-30rpx">
      <view class="save-btn" @tap="saveIntention">保存</view>
    </view>
  </view>
</template>

<script setup lang="ts">
const selectedJobs = ref([]);
const MAX_SELECTION = 5;

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 职位分类数据
const jobCategories = [
  {
    name: "热门职位",
    jobs: [
      { id: 1, name: "销售" },
      { id: 2, name: "客服" },
      { id: 3, name: "行政" },
      { id: 4, name: "财务" },
      { id: 5, name: "人事" },
      { id: 6, name: "前台" },
      { id: 7, name: "司机" },
    ],
  },
  {
    name: "餐饮/酒店",
    jobs: [
      { id: 8, name: "服务员" },
      { id: 9, name: "厨师" },
      { id: 10, name: "收银员" },
      { id: 11, name: "配菜" },
      { id: 12, name: "前厅经理" },
      { id: 13, name: "后厨主管" },
    ],
  },
  {
    name: "互联网/IT",
    jobs: [
      { id: 14, name: "程序员" },
      { id: 15, name: "UI设计师" },
      { id: 16, name: "产品经理" },
      { id: 17, name: "运营" },
      { id: 18, name: "测试" },
      { id: 19, name: "前端开发" },
      { id: 20, name: "后端开发" },
    ],
  },
  {
    name: "制造/生产",
    jobs: [
      { id: 21, name: "普工" },
      { id: 22, name: "技工" },
      { id: 23, name: "质检员" },
      { id: 24, name: "机修工" },
      { id: 25, name: "电工" },
      { id: 26, name: "车间主管" },
    ],
  },
  {
    name: "零售/贸易",
    jobs: [
      { id: 27, name: "导购" },
      { id: 28, name: "店员" },
      { id: 29, name: "店长" },
      { id: 30, name: "采购" },
      { id: 31, name: "仓管" },
      { id: 32, name: "物流专员" },
    ],
  },
];

// 页面加载时从本地存储读取已选职位
onLoad(() => {
  try {
    const storedJobs = uni.getStorageSync("intentionJobs");
    if (storedJobs) {
      selectedJobs.value = JSON.parse(storedJobs);
    }
  } catch (e) {
    console.error("读取意向职位失败", e);
  }
});

// 检查职位是否已被选择
const isJobSelected = (job) => {
  return selectedJobs.value.some((item) => item.id === job.id);
};

// 切换职位选择状态
const toggleJobSelection = (job) => {
  const index = selectedJobs.value.findIndex((item) => item.id === job.id);

  if (index > -1) {
    // 已选中，则取消选择
    selectedJobs.value.splice(index, 1);
  } else {
    // 未选中，判断是否达到上限
    if (selectedJobs.value.length >= MAX_SELECTION) {
      uni.showToast({
        title: `最多只能选择${MAX_SELECTION}个意向职位`,
        icon: "none",
      });
      return;
    }
    // 添加到已选列表
    selectedJobs.value.push(job);
  }
};

// 移除已选职位
const removeJob = (index) => {
  selectedJobs.value.splice(index, 1);
};

// 清空所有已选职位
const clearAllSelected = () => {
  uni.showModal({
    title: "提示",
    content: "确定要清空所有已选职位吗？",
    success: (res) => {
      if (res.confirm) {
        selectedJobs.value = [];
      }
    },
  });
};

// 保存意向职位设置
const saveIntention = () => {
  try {
    uni.setStorageSync("intentionJobs", JSON.stringify(selectedJobs.value));
    uni.showToast({
      title: "保存成功",
      icon: "success",
    });
    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  } catch (e) {
    console.error("保存意向职位失败", e);
    uni.showToast({
      title: "保存失败，请重试",
      icon: "none",
    });
  }
};
</script>

<style lang="scss" scoped>
.container {
  background-color: var(--bg-page);
  min-height: 100vh;

  // 已选职位区域
  .selected-jobs {
    background-color: #fff;
    border-radius: 16rpx;

    .empty-tips {
      border: 1px dashed #ddd;
      border-radius: 8rpx;
    }

    .selected-list {
      gap: 20rpx;
    }

    .selected-item {
      padding: 16rpx 24rpx;
      background-color: rgba($primary, 0.1);
      border-radius: 8rpx;
      min-width: 200rpx;

      .job-name {
        font-size: 28rpx;
        color: $text-base;
        margin-right: 16rpx;
      }

      .remove-icon {
        font-size: 32rpx;
        color: $primary;
      }
    }
  }

  // 职位分类区域
  .job-categories {
    background-color: #fff;
    border-radius: 16rpx;
    padding-bottom: 150rpx;

    .category-item {
      border-bottom: 1px solid #f5f5f5;
      padding-bottom: 20rpx;
      margin-bottom: 20rpx;

      &:last-child {
        border-bottom: none;
      }
    }

    .job-list {
      gap: 20rpx;
    }

    .job-item {
      padding: 16rpx 24rpx;
      background-color: #f5f5f5;
      border-radius: 8rpx;
      font-size: 28rpx;
      color: $text-secondary;
      transition: all 0.2s;

      &-selected {
        background-color: rgba($primary, 0.1);
        color: $primary;
        border: 1px solid $primary;
      }
    }
  }

  // 底部保存按钮
  .save-btn-wrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding-top: 20rpx;
    padding-bottom: env(safe-area-inset-bottom, 40rpx);
    background-color: #fff;
    border-top: 1px solid #f5f5f5;

    .save-btn {
      background: linear-gradient(90deg, $primary, darken($primary, 10%));
      height: 90rpx;
      border-radius: 45rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 32rpx;
      font-weight: 500;
      box-shadow: 0 6rpx 12rpx rgba($primary, 0.3);
      transition: all 0.2s;

      &:active {
        transform: scale(0.98);
        opacity: 0.9;
      }
    }
  }
}
</style>
